export const FILE_TYPES = {
    IMAGE: 'image',
    VIDEO: 'video',
    AUDIO: 'audio',
    DOCUMENT: 'document',
    ATTACHMENT: 'attachment',
} as const;

export type FileType = (typeof FILE_TYPES)[keyof typeof FILE_TYPES];

export const MIME_TYPES = {
    image: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif'],
    video: [
        'video/mp4',
        'video/webm',
        'video/ogg',
        'video/avi',
        'video/mov',
        'video/wmv',
        'video/flv',
        'video/mkv',
        'video/m4v',
        'video/3gp',
    ],
    audio: ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/m4a', 'audio/mpeg'],
    document: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    ],
    attachment: [
        'application/zip',
        'application/x-zip-compressed',
        'application/x-rar-compressed',
        'application/x-7z-compressed',
        'application/x-tar',
        'application/gzip',
    ],
};

export const getFileTypeFromMimeType = (mimeType: string): FileType => {
    for (const [type, mimes] of Object.entries(MIME_TYPES)) {
        if (mimes.includes(mimeType)) {
            return type as FileType;
        }
    }
    return FILE_TYPES.ATTACHMENT; // default
};

export const getFileIcon = (fileType: FileType) => {
    switch (fileType) {
        case FILE_TYPES.IMAGE:
            return 'Image';
        case FILE_TYPES.VIDEO:
            return 'Video';
        case FILE_TYPES.AUDIO:
            return 'Music';
        case FILE_TYPES.DOCUMENT:
            return 'FileText';
        case FILE_TYPES.ATTACHMENT:
            return 'File';
        default:
            return 'File';
    }
};

export const getFileDisplayName = (fileType: FileType) => {
    switch (fileType) {
        case FILE_TYPES.IMAGE:
            return 'Hình ảnh';
        case FILE_TYPES.VIDEO:
            return 'Video';
        case FILE_TYPES.AUDIO:
            return 'Âm thanh';
        case FILE_TYPES.DOCUMENT:
            return 'Tài liệu';
        case FILE_TYPES.ATTACHMENT:
            return 'Tệp đính kèm';
        default:
            return 'File';
    }
};
