import { keepPreviousData } from '@tanstack/react-query';
import { LIMIT_MAX, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { isUndefined, omitBy } from 'lodash';
import { useEffect, useMemo } from 'react';
import { Plus, Trash2 } from 'react-feather';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { useSearchParams } from 'react-router-dom';
import Select from 'react-select';
import { ARTICLE_ROYALTIES_LIST } from 'services/ArticleService';
import { ROYALTY_PARAMS_LIST } from 'services/RoyaltyParam';
import { USER_LIST } from 'services/UserService';
import { useAppStore } from 'stores/appStore';
import Article, { ArticleRoyaltiesQueryRes, ArticleRoyalty, RoyaltyTypeOptions } from 'types/Article';
import { baseFilterConfig } from 'types/common';
import { ItemStatus } from 'types/common/Item';
import { RoyaltyParamsQuery } from 'types/RoyaltyParams';
import { UserListQuery } from 'types/User';
import { generateFilters } from 'utils/common';
import { ArticleRoyaltyTypeListQuery } from '../../../types/ArticleRoyaltyType';
import { ARTICLE_ROYALTY_TYPE_LIST } from '../../../services/ArticleRoyaltyTypeService';

type Props = {
    articleId: number | null;
    article: Article | null;
    onCloseRoyaltyModal: (isOpen: boolean) => void;
};

type FormValues = {
    article_royalties: ArticleRoyalty[];
};

const ArticleRoyaltyContent = ({ articleId, onCloseRoyaltyModal, article }: Readonly<Props>) => {
    const departmentId = useAppStore((state) => state.departmentId);
    const [searchParams] = useSearchParams();
    const article_type_id = searchParams.get('article_type_id');

    const { data: articleRoyaltyTypeData } = useGraphQLQuery<ArticleRoyaltyTypeListQuery>(
        [QUERY_KEY.ARTICLE_ROYALTY_TYPES, article_type_id],
        ARTICLE_ROYALTY_TYPE_LIST,
        {
            limit: LIMIT_MAX,
            page: 1,
            filters: article_type_id ? [`article_type_id:=(${article_type_id})`] : [],
        }
    );
    const articleRoyaltyType = useMemo(
        () => articleRoyaltyTypeData?.article_royalty_types_list.data,
        [articleRoyaltyTypeData]
    );

    const form = useForm<FormValues>({
        defaultValues: {
            article_royalties: [],
        },
    });

    const { control, handleSubmit, reset, watch, setValue } = form;

    // Fetch article royalties data
    const { data: articleRoyaltiesData } = useGraphQLQuery<ArticleRoyaltiesQueryRes>(
        [QUERY_KEY.ARTICLE_ROYALTIES, articleId],
        ARTICLE_ROYALTIES_LIST,
        {
            filters: [`article_id:=(${articleId})`],
        },
        '',
        {
            enabled: !!articleId,
        }
    );

    const articleRoyalties = useMemo(
        () => articleRoyaltiesData?.article_royalties_list?.data || [],
        [articleRoyaltiesData]
    );

    // Fetch royalty params
    const paramConfig = omitBy(
        {
            limit: LIMIT_MAX,
            page: 1,
            status_id: ItemStatus.ACTIVE,
            department_id: departmentId.toString(),
        },
        isUndefined
    );

    const { limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, baseFilterConfig);

    const { data: royaltyParamsData } = useGraphQLQuery<RoyaltyParamsQuery>(
        [QUERY_KEY.ROYALTY_PARAMS, paramConfig, filters],
        ROYALTY_PARAMS_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            filters: filters.length > 0 ? filters : undefined,
            sorts: ['display_order:ASC'],
        },
        '',
        {
            enabled: !!paramConfig.department_id,
            placeholderData: keepPreviousData,
        }
    );

    const royaltyParams = useMemo(() => royaltyParamsData?.royalty_params_list?.data || [], [royaltyParamsData]);

    // Fetch users
    const { data: userData } = useGraphQLQuery<UserListQuery>(
        [QUERY_KEY.USERS],
        USER_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [`status_id:=(${ItemStatus.ACTIVE})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const userOptions = useMemo(
        () =>
            userData?.users_list?.data?.map((u) => ({
                label: u.full_name,
                value: u.id || 0,
            })) || [],
        [userData]
    );

    const articleRoyaltyTypeOptions = useMemo(
        () =>
            articleRoyaltyType?.map((type) => ({
                label: type.name,
                value: type.id || 0,
            })) || [],
        [articleRoyaltyType]
    );

    // Initialize form data based on RoyaltyTypeOptions
    const initializeFormData = useMemo(() => {
        const formData: ArticleRoyalty[] = [];

        RoyaltyTypeOptions.forEach((typeOption) => {
            const existingData = articleRoyalties.find((item) => item.type_id === typeOption.value);

            if (existingData) {
                formData.push(existingData);
            } else {
                formData.push({
                    id: 0,
                    article_id: articleId,
                    type_id: typeOption.value,
                    suggest_royalty: 0,
                    title: article?.title || '',
                    royalty_type_id: undefined,
                    article_statistic: 0,
                    articleRoyaltyUsers: [],
                });
            }
        });

        return formData;
    }, [articleRoyalties, articleId, article]);

    // Update form when data changes
    useEffect(() => {
        reset({
            article_royalties: initializeFormData,
        });

        // Khởi tạo các field riêng biệt cho validation
        initializeFormData.forEach((royalty, royaltyIndex) => {
            royalty.articleRoyaltyUsers.forEach((user, userIndex) => {
                user.param_config?.forEach((config) => {
                    const fieldName = `article_royalties.${royaltyIndex}.articleRoyaltyUsers.${userIndex}.param_${config.label}`;
                    //tslint:disable-next-line:no-any
                    setValue(fieldName as any, config.value);
                });
            });
        });
    }, [initializeFormData, reset, setValue]);

    // Helper functions
    const addUser = (royaltyIndex: number) => {
        const currentRoyalties = watch('article_royalties') || [];

        const updatedRoyalties = [...currentRoyalties];

        // Tạo param_config với option đầu tiên được chọn sẵn
        const defaultParamConfig = royaltyParams.map((param) => ({
            label: param.id!,
            value: param.options[0]?.value || '',
        }));

        const newUser = {
            id: 0,
            article_royalty_id: updatedRoyalties[royaltyIndex].id || 0,
            user_id: 0,
            percent: 0,
            comment: '',
            param_config: defaultParamConfig,
            final_royalty: 0,
            suggest_royalty: 0,
        };

        updatedRoyalties[royaltyIndex].articleRoyaltyUsers.push(newUser);

        // Khởi tạo các field riêng biệt cho validation
        const userIndex = updatedRoyalties[royaltyIndex].articleRoyaltyUsers.length - 1;
        const resetData = { article_royalties: updatedRoyalties };

        // Thêm các field riêng biệt cho mỗi param
        royaltyParams.forEach((param) => {
            const fieldName = `article_royalties.${royaltyIndex}.articleRoyaltyUsers.${userIndex}.param_${param.id}`;
            //tslint:disable-next-line:no-any
            setValue(fieldName as any, param.options[0]?.value || '');
        });

        reset(resetData);
    };

    const removeUser = (royaltyIndex: number, userIndex: number) => {
        const currentRoyalties = watch('article_royalties') || [];
        const updatedRoyalties = [...currentRoyalties];

        updatedRoyalties[royaltyIndex].articleRoyaltyUsers.splice(userIndex, 1);

        reset({ article_royalties: updatedRoyalties });
    };

    const onSubmit = (data: FormValues) => {
        // Filter only royalties that have users
        const filteredRoyalties = data.article_royalties.filter((royalty) => royalty.articleRoyaltyUsers.length > 0);

        // console.log('Submit data:', { article_royalties: filteredRoyalties });
        // TODO: Call API to save data
    };

    return (
        <FormProvider {...form}>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="card-body">
                    <div className="table-responsive mb-2">
                        <table className="table table-sm align-middle">
                            <thead>
                                <tr>
                                    <th style={{ width: '80px' }}>Nhóm</th>
                                    <th style={{ width: '200px' }}>Tiêu đề</th>
                                    <th style={{ width: '150px' }}>Thể loại</th>
                                    <th style={{ width: '100px' }}>Thời lượng</th>
                                    <th>Người thực hiện</th>
                                    {royaltyParams.map((param) => (
                                        <th key={param.id}>{param.name}</th>
                                    ))}
                                    <th style={{ width: '100px' }}>Đề xuất</th>
                                    <th style={{ width: '100px' }}>Điểm</th>
                                    <th style={{ width: '150px' }}>Ghi chú</th>
                                    <th className="text-center" style={{ width: '48px' }}>
                                        Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {watch('article_royalties')?.map((royalty, royaltyIndex) => {
                                    const royaltyTypeOption = RoyaltyTypeOptions.find(
                                        (opt) => opt.value === royalty.type_id
                                    );
                                    const usersCount = royalty.articleRoyaltyUsers.length;
                                    const showAddButton = usersCount === 0;

                                    if (showAddButton) {
                                        // Show add button when no users
                                        return (
                                            <tr key={royaltyIndex}>
                                                <td>{royaltyTypeOption?.label}</td>
                                                <td>
                                                    <Controller
                                                        name={`article_royalties.${royaltyIndex}.title`}
                                                        control={control}
                                                        rules={{
                                                            required: true,
                                                        }}
                                                        render={({ field, fieldState }) => (
                                                            <div>
                                                                <input
                                                                    className={`form-control ${
                                                                        fieldState.error ? 'is-invalid' : ''
                                                                    }`}
                                                                    type="text"
                                                                    value={field.value}
                                                                    onChange={field.onChange}
                                                                />
                                                            </div>
                                                        )}
                                                    />
                                                </td>
                                                <td>
                                                    <Controller
                                                        name={`article_royalties.${royaltyIndex}.royalty_type_id`}
                                                        control={control}
                                                        rules={{
                                                            validate: (value) => {
                                                                // Chỉ validate khi có ít nhất 1 user trong articleRoyaltyUsers
                                                                const currentRoyalty = watch(
                                                                    `article_royalties.${royaltyIndex}`
                                                                );
                                                                const hasUsers =
                                                                    currentRoyalty?.articleRoyaltyUsers?.length > 0;

                                                                if (hasUsers) {
                                                                    return !!value && value !== 0;
                                                                }
                                                                return true; // Không validate nếu chưa có user
                                                            },
                                                        }}
                                                        render={({ field, fieldState }) => (
                                                            <div>
                                                                <Select
                                                                    options={articleRoyaltyTypeOptions}
                                                                    value={articleRoyaltyTypeOptions.find(
                                                                        (opt) => opt.value === field.value
                                                                    )}
                                                                    onChange={(option) =>
                                                                        field.onChange(option?.value || 0)
                                                                    }
                                                                    menuPortalTarget={document.body}
                                                                    styles={{
                                                                        menuPortal: (base) => ({
                                                                            ...base,
                                                                            zIndex: 1360,
                                                                        }),
                                                                        control: (base) => ({
                                                                            ...base,
                                                                            borderColor: fieldState.error
                                                                                ? '#dc3545'
                                                                                : base.borderColor,
                                                                        }),
                                                                    }}
                                                                />
                                                            </div>
                                                        )}
                                                    />
                                                </td>
                                                <td>
                                                    <Controller
                                                        name={`article_royalties.${royaltyIndex}.article_statistic`}
                                                        control={control}
                                                        rules={{
                                                            min: {
                                                                value: 0,
                                                                message: '',
                                                            },
                                                        }}
                                                        render={({ field, fieldState }) => (
                                                            <div>
                                                                <input
                                                                    className={`form-control ${
                                                                        fieldState.error ? 'is-invalid' : ''
                                                                    }`}
                                                                    type="number"
                                                                    value={field.value}
                                                                    onChange={field.onChange}
                                                                />
                                                            </div>
                                                        )}
                                                    />
                                                </td>
                                                <td>
                                                    <button
                                                        type="button"
                                                        onClick={() => addUser(royaltyIndex)}
                                                        className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                                        title="Thêm người thực hiện"
                                                    >
                                                        <Plus size={14} />
                                                    </button>
                                                </td>
                                                {royaltyParams.map((param) => (
                                                    <td key={param.id}></td>
                                                ))}
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                        );
                                    }

                                    // Show users rows with add button at the end
                                    const userRows = royalty.articleRoyaltyUsers.map((user, userIndex) => (
                                        <tr key={`${royaltyIndex}-${userIndex}`}>
                                            {userIndex === 0 && (
                                                <>
                                                    <td rowSpan={usersCount + 1}>{royaltyTypeOption?.label}</td>
                                                    <td rowSpan={usersCount + 1}>
                                                        <Controller
                                                            name={`article_royalties.${royaltyIndex}.title`}
                                                            control={control}
                                                            rules={{
                                                                required: true,
                                                            }}
                                                            render={({ field, fieldState }) => (
                                                                <div>
                                                                    <input
                                                                        className={`form-control ${
                                                                            fieldState.error ? 'is-invalid' : ''
                                                                        }`}
                                                                        type="text"
                                                                        value={field.value}
                                                                        onChange={field.onChange}
                                                                    />
                                                                </div>
                                                            )}
                                                        />
                                                    </td>
                                                    <td rowSpan={usersCount + 1}>
                                                        <Controller
                                                            name={`article_royalties.${royaltyIndex}.royalty_type_id`}
                                                            control={control}
                                                            rules={{
                                                                validate: (value) => {
                                                                    // Chỉ validate khi có ít nhất 1 user trong articleRoyaltyUsers
                                                                    const currentRoyalty = watch(
                                                                        `article_royalties.${royaltyIndex}`
                                                                    );
                                                                    const hasUsers =
                                                                        currentRoyalty?.articleRoyaltyUsers?.length > 0;

                                                                    if (hasUsers) {
                                                                        return !!value && value !== 0;
                                                                    }
                                                                    return true; // Không validate nếu chưa có user
                                                                },
                                                            }}
                                                            render={({ field, fieldState }) => (
                                                                <div>
                                                                    <Select
                                                                        options={articleRoyaltyTypeOptions}
                                                                        value={articleRoyaltyTypeOptions.find(
                                                                            (opt) => opt.value === field.value
                                                                        )}
                                                                        onChange={(option) =>
                                                                            field.onChange(option?.value || 0)
                                                                        }
                                                                        menuPortalTarget={document.body}
                                                                        styles={{
                                                                            menuPortal: (base) => ({
                                                                                ...base,
                                                                                zIndex: 1360,
                                                                            }),
                                                                            control: (base) => ({
                                                                                ...base,
                                                                                borderColor: fieldState.error
                                                                                    ? '#dc3545'
                                                                                    : base.borderColor,
                                                                            }),
                                                                        }}
                                                                    />
                                                                </div>
                                                            )}
                                                        />
                                                    </td>
                                                    <td rowSpan={usersCount + 1}>
                                                        <Controller
                                                            name={`article_royalties.${royaltyIndex}.article_statistic`}
                                                            control={control}
                                                            rules={{
                                                                min: {
                                                                    value: 0,
                                                                    message: '',
                                                                },
                                                            }}
                                                            render={({ field, fieldState }) => (
                                                                <div>
                                                                    <input
                                                                        className={`form-control ${
                                                                            fieldState.error ? 'is-invalid' : ''
                                                                        }`}
                                                                        type="number"
                                                                        value={field.value}
                                                                        onChange={field.onChange}
                                                                    />
                                                                </div>
                                                            )}
                                                        />
                                                    </td>
                                                </>
                                            )}
                                            <td>
                                                <Controller
                                                    name={`article_royalties.${royaltyIndex}.articleRoyaltyUsers.${userIndex}.user_id`}
                                                    control={control}
                                                    rules={{
                                                        required: true,
                                                        validate: (value) => !!value && value !== 0,
                                                    }}
                                                    render={({ field, fieldState }) => (
                                                        <div>
                                                            <Select
                                                                options={userOptions}
                                                                value={userOptions.find(
                                                                    (opt) => opt.value === field.value
                                                                )}
                                                                onChange={(option) =>
                                                                    field.onChange(option?.value || 0)
                                                                }
                                                                menuPortalTarget={document.body}
                                                                styles={{
                                                                    menuPortal: (base) => ({
                                                                        ...base,
                                                                        zIndex: 1360,
                                                                    }),
                                                                    control: (base) => ({
                                                                        ...base,
                                                                        borderColor: fieldState.error
                                                                            ? '#dc3545'
                                                                            : base.borderColor,
                                                                    }),
                                                                }}
                                                            />
                                                        </div>
                                                    )}
                                                />
                                            </td>
                                            {royaltyParams.map((param, paramIndex) => {
                                                // Tạo unique field name cho mỗi param để validation riêng biệt
                                                const fieldName = `article_royalties.${royaltyIndex}.articleRoyaltyUsers.${userIndex}.param_${param.id}`;

                                                return (
                                                    <td key={param.id}>
                                                        <Controller
                                                            //tslint:disable-next-line:no-any
                                                            name={fieldName as any}
                                                            control={control}
                                                            rules={{
                                                                validate: (value) => !!value,
                                                            }}
                                                            render={({ field, fieldState }) => {
                                                                const paramOptions = param.options.map((val) => ({
                                                                    label: val.name,
                                                                    value: val.value!,
                                                                }));

                                                                // Lấy giá trị hiện tại từ param_config array
                                                                const paramConfigArray =
                                                                    watch(
                                                                        `article_royalties.${royaltyIndex}.articleRoyaltyUsers.${userIndex}.param_config`
                                                                    ) || [];
                                                                const currentConfig = paramConfigArray.find(
                                                                    (config) => config.label === param.id
                                                                );

                                                                return (
                                                                    <div>
                                                                        <Select
                                                                            options={paramOptions}
                                                                            value={paramOptions.find(
                                                                                (opt) =>
                                                                                    opt.value === currentConfig?.value
                                                                            )}
                                                                            onChange={(option) => {
                                                                                // Cập nhật field riêng biệt cho validation
                                                                                field.onChange(option?.value || '');

                                                                                // Cập nhật param_config array
                                                                                const updatedConfig =
                                                                                    paramConfigArray.filter(
                                                                                        (config) =>
                                                                                            config.label !== param.id
                                                                                    );
                                                                                if (option) {
                                                                                    updatedConfig.push({
                                                                                        label: param.id!,
                                                                                        value: option.value,
                                                                                    });
                                                                                }
                                                                                setValue(
                                                                                    `article_royalties.${royaltyIndex}.articleRoyaltyUsers.${userIndex}.param_config`,
                                                                                    updatedConfig
                                                                                );
                                                                            }}
                                                                            menuPortalTarget={document.body}
                                                                            styles={{
                                                                                menuPortal: (base) => ({
                                                                                    ...base,
                                                                                    zIndex: 1360,
                                                                                }),
                                                                                control: (base) => ({
                                                                                    ...base,
                                                                                    borderColor: fieldState.error
                                                                                        ? '#dc3545'
                                                                                        : base.borderColor,
                                                                                }),
                                                                            }}
                                                                        />
                                                                    </div>
                                                                );
                                                            }}
                                                        />
                                                    </td>
                                                );
                                            })}
                                            <td>
                                                <Controller
                                                    name={`article_royalties.${royaltyIndex}.articleRoyaltyUsers.${userIndex}.suggest_royalty`}
                                                    control={control}
                                                    rules={{
                                                        min: {
                                                            value: 0,
                                                            message: '',
                                                        },
                                                    }}
                                                    render={({ field, fieldState }) => (
                                                        <div>
                                                            <input
                                                                className={`form-control ${
                                                                    fieldState.error ? 'is-invalid' : ''
                                                                }`}
                                                                type="number"
                                                                value={field.value}
                                                                onChange={field.onChange}
                                                            />
                                                        </div>
                                                    )}
                                                />
                                            </td>
                                            <td>
                                                <Controller
                                                    name={`article_royalties.${royaltyIndex}.articleRoyaltyUsers.${userIndex}.final_royalty`}
                                                    control={control}
                                                    rules={{
                                                        required: true,
                                                        min: { value: 0, message: '' },
                                                    }}
                                                    render={({ field, fieldState }) => (
                                                        <div>
                                                            <input
                                                                className={`form-control ${
                                                                    fieldState.error ? 'is-invalid' : ''
                                                                }`}
                                                                type="number"
                                                                value={field.value}
                                                                onChange={field.onChange}
                                                            />
                                                        </div>
                                                    )}
                                                />
                                            </td>
                                            <td>
                                                <Controller
                                                    name={`article_royalties.${royaltyIndex}.articleRoyaltyUsers.${userIndex}.comment`}
                                                    control={control}
                                                    render={({ field }) => (
                                                        <input
                                                            className="form-control"
                                                            value={field.value}
                                                            onChange={field.onChange}
                                                        />
                                                    )}
                                                />
                                            </td>
                                            <td className="text-center">
                                                <button
                                                    type="button"
                                                    onClick={() => removeUser(royaltyIndex, userIndex)}
                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                    title="Xóa"
                                                >
                                                    <Trash2 size={14} />
                                                </button>
                                            </td>
                                        </tr>
                                    ));

                                    // Add button row at the end of each group
                                    const addButtonRow = (
                                        <tr key={`${royaltyIndex}-add`}>
                                            <td>
                                                <button
                                                    type="button"
                                                    onClick={() => addUser(royaltyIndex)}
                                                    className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                                    title="Thêm người thực hiện"
                                                >
                                                    <Plus size={14} />
                                                </button>
                                            </td>
                                            {royaltyParams.map((param) => (
                                                <td key={param.id}></td>
                                            ))}
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                    );

                                    return [...userRows, addButtonRow];
                                })}
                            </tbody>
                        </table>
                    </div>
                    <div className="flex justify-end">
                        <button
                            type="button"
                            className="btn btn-secondary mr-2"
                            onClick={() => onCloseRoyaltyModal(false)}
                        >
                            Hủy
                        </button>
                        <button
                            type="submit"
                            className="btn btn-primary"
                            disabled={
                                !watch('article_royalties')?.some((royalty) => royalty.articleRoyaltyUsers.length > 0)
                            }
                        >
                            Cập nhật
                        </button>
                    </div>
                </div>
            </form>
        </FormProvider>
    );
};

export default ArticleRoyaltyContent;
