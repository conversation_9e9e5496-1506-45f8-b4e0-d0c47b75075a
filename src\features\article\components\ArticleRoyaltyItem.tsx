import FormatNumber from 'components/partials/FormatNumber';
import { ARTICLE_TAB } from 'constants/common';
import { useCallback, useMemo } from 'react';
import { Eye, X } from 'react-feather';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import Select from 'react-select';
import { ArticleRoyaltyUser, RoyaltyType } from 'types/Article';
import { SelectOption } from 'types/common/Item';
import cn from 'utils/cn';

type Props = {
    typeId: RoyaltyType;
    setTabAction(tab: ARTICLE_TAB | null): void;
    userOptions: SelectOption[];
    articleRoyaltyTypeOptions: SelectOption[];
    articleRoyaltyFieldName: string;
    onViewUserFiles: (user?: SelectOption) => void;
};

const ArticleRoyaltyItem = ({
    typeId,
    setTabAction,
    userOptions,
    articleRoyaltyTypeOptions,
    articleRoyaltyFieldName,
    onViewUserFiles,
}: Props) => {
    const { control, watch } = useFormContext();

    const fieldName = `${articleRoyaltyFieldName}.article_royalty_users`;

    const { append, remove } = useFieldArray({
        control,
        name: fieldName,
    });

    const royaltyTypeId = watch(`${articleRoyaltyFieldName}.royalty_type_id`);
    const articleRoyaltyUsers: ArticleRoyaltyUser[] = watch(fieldName) || [];

    const royaltyType = useMemo(
        () => articleRoyaltyTypeOptions.find((o) => o.value === royaltyTypeId),
        [royaltyTypeId, articleRoyaltyTypeOptions]
    );

    const getTitle = useCallback(() => {
        switch (typeId) {
            case RoyaltyType.Content:
                return 'Biên tập';

            case RoyaltyType.Media:
                return 'Media';

            default:
                return 'Hỗ trợ';
        }
    }, [typeId]);

    const onAddUser = () => {
        if (typeId === RoyaltyType.Media) return setTabAction(ARTICLE_TAB.MEDIA);

        append({
            user_id: 0,
            percent: 0,
            suggest_royalty: 0,
            comment: '',
        });
    };

    return (
        <div className="mb-2">
            <div className="mb-1">
                <div className="d-flex justify-content-between flex-md-row flex-column mb-1">
                    <label className="form-label font-bold">{getTitle()}</label>
                    <button
                        onClick={onAddUser}
                        type="button"
                        className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                    >
                        Thêm tác giả
                    </button>
                </div>
                <Controller
                    name={fieldName}
                    control={control}
                    render={({ fieldState: { error } }) => (
                        <>
                            <div className="table-responsive">
                                <table
                                    className="table"
                                    style={{
                                        tableLayout: 'fixed',
                                    }}
                                >
                                    <thead>
                                        <tr>
                                            {typeId === RoyaltyType.Media && (
                                                <th className="text-center !px-[4px] w-[104px]">File</th>
                                            )}
                                            <th className="text-center !px-[4px] w-[144px] ">Tác giả</th>
                                            <th className="text-center !px-[4px] w-[68px]">
                                                {typeId === RoyaltyType.Content ? 'Tỉ lệ' : 'Đề xuất'}
                                            </th>
                                            <th className="text-center !px-[4px] w-[112px]">Ghi chú</th>
                                            <th
                                                className={cn('text-center !px-[4px] w-[34px]', {
                                                    'w-[68px]': typeId === RoyaltyType.Media,
                                                })}
                                            ></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {articleRoyaltyUsers.map((field, index) => (
                                            <tr key={index}>
                                                {typeId === RoyaltyType.Media && (
                                                    <td className="!px-[4px] truncate">{field.file_name}</td>
                                                )}

                                                <td className="!px-[4px]">
                                                    <Controller
                                                        name={`${fieldName}.${index}.user_id`}
                                                        control={control}
                                                        render={({ field: { value, onChange } }) => (
                                                            <Select
                                                                options={userOptions}
                                                                isClearable={false}
                                                                menuPortalTarget={document.body}
                                                                onChange={(newValue) => onChange(newValue?.value)}
                                                                value={userOptions.find((u) => u.value === value)}
                                                            />
                                                        )}
                                                    />
                                                </td>
                                                {typeId === RoyaltyType.Content ? (
                                                    <td className="text-center !px-[4px]">
                                                        <Controller
                                                            name={`${fieldName}.${index}.percent`}
                                                            control={control}
                                                            render={({ field: { value, onChange } }) => (
                                                                <FormatNumber
                                                                    value={value}
                                                                    isInput={true}
                                                                    onValueChange={(newValue: number) =>
                                                                        onChange(newValue ? newValue : '')
                                                                    }
                                                                    maxLength={3}
                                                                />
                                                            )}
                                                        />
                                                    </td>
                                                ) : (
                                                    <td className="text-center !px-[4px]">
                                                        <Controller
                                                            name={`${fieldName}.${index}.suggest_royalty`}
                                                            control={control}
                                                            render={({ field: { value, onChange } }) => (
                                                                <FormatNumber
                                                                    value={value}
                                                                    isInput={true}
                                                                    onValueChange={(newValue: number) =>
                                                                        onChange(newValue ? newValue : '')
                                                                    }
                                                                />
                                                            )}
                                                        />
                                                    </td>
                                                )}
                                                <td className="text-center !px-[4px]">
                                                    <Controller
                                                        name={`${fieldName}.${index}.comment`}
                                                        control={control}
                                                        render={({ field: { value, onChange } }) => (
                                                            <input
                                                                value={value}
                                                                onChange={onChange}
                                                                type="text"
                                                                className="form-control"
                                                            />
                                                        )}
                                                    />
                                                </td>
                                                <td className="text-center !px-[4px]">
                                                    <div className="d-flex">
                                                        {typeId === RoyaltyType.Media && (
                                                            <button
                                                                onClick={() =>
                                                                    onViewUserFiles(
                                                                        userOptions.find(
                                                                            (u) =>
                                                                                u.value ===
                                                                                (watch(
                                                                                    `${fieldName}.${index}.user_id`
                                                                                ) || 0)
                                                                        )
                                                                    )
                                                                }
                                                                type="button"
                                                                title="Xem media"
                                                                className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                            >
                                                                <Eye size={14} />
                                                            </button>
                                                        )}
                                                        <button
                                                            onClick={() => remove(index)}
                                                            type="button"
                                                            title="Xoá"
                                                            className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                        >
                                                            <X size={14} />
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                            <span className="error">{error?.message}</span>
                        </>
                    )}
                />
            </div>
            {typeId === RoyaltyType.Content && (
                <>
                    <div className="mb-1">
                        <label className="form-label">Loại tin</label>
                        <Controller
                            name={`${articleRoyaltyFieldName}.royalty_type_id`}
                            control={control}
                            render={({ field: { value, onChange } }) => (
                                <Select
                                    options={articleRoyaltyTypeOptions}
                                    onChange={(newValue) => onChange(newValue?.value)}
                                    value={articleRoyaltyTypeOptions.find((opt) => opt.value === value)}
                                />
                            )}
                        />
                    </div>
                    <div className="mb-1">
                        <label className="form-label">Nhuận bút đề xuất</label>
                        <Controller
                            name={`${articleRoyaltyFieldName}.suggest_royalty`}
                            control={control}
                            render={({ field }) => (
                                <FormatNumber
                                    value={field.value || ''}
                                    isInput={true}
                                    onValueChange={(newValue: number) => field.onChange(newValue)}
                                    placeholder={
                                        royaltyType
                                            ? `${royaltyType?.fromRoyalty} - ${royaltyType?.toRoyalty}`
                                            : undefined
                                    }
                                />
                            )}
                        />
                    </div>
                </>
            )}
        </div>
    );
};

export default ArticleRoyaltyItem;
