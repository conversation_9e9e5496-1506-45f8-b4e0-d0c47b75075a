import { yupResolver } from '@hookform/resolvers/yup';
import { keepPreviousData, useQueryClient } from '@tanstack/react-query';
import ModalConfirm from 'components/partials/ModalConfirm';
import { LIMIT_MAX, QUERY_KEY } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { isEmpty } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { Plus, Save, Trash2 } from 'react-feather';
import { Controller, FieldError, useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Select from 'react-select';
import {
    ROYALTY_PARAMS_OPTIONS_CREATE,
    ROYALTY_PARAMS_OPTIONS_DELETE,
    ROYALTY_PARAMS_OPTIONS_UPDATE,
} from 'services/RoyaltyParam';
import { USER_LIST } from 'services/UserService';
import { ItemStatus } from 'types/common/Item';
import { RoyaltyParamOption, RoyaltyParamOptionsForm } from 'types/RoyaltyParams';
import { UserListQuery } from 'types/User';
import { showToast } from 'utils/common';
import * as yup from 'yup';

type Props = {
    options: RoyaltyParamOptionsForm[];
    royaltyParamId?: number;
};

const schema = yup
    .object({
        options: yup.array().of(
            yup.object({
                display_order: yup.string().trim().required('error.required').typeError('error.number'),
                name: yup.string().trim().required('error.required'),
                value: yup.string().trim().required('error.required'),
                ratio: yup.string().trim().required('error.required').typeError('error.number'),
                user_ids: yup.array().of(yup.number()).optional(),
            })
        ),
    })
    .required();

const RoyaltyParamOptionsTable = ({ options, royaltyParamId }: Props) => {
    const { t } = useTranslation();
    const queryClient = useQueryClient();
    const [isOpenWarningDelete, setIsOpenWarningDelete] = useState(false);
    const [selectedRowId, setSelectedRowId] = useState(0);
    const [fieldState, setFieldState] = useState<{
        error?: {
            [key: number]: {
                display_order?: FieldError;
                name?: FieldError;
                value?: FieldError;
                ratio?: FieldError;
            };
        };
    }>({});

    const {
        control,
        reset,
        getValues,
        formState: { errors },
        getFieldState,
        trigger,
        watch,
    } = useForm<{
        options: RoyaltyParamOptionsForm[];
    }>({
        resolver: yupResolver(schema),
        defaultValues: {
            options: [],
        },
    });

    const { fields, append, remove } = useFieldArray({
        name: 'options',
        control,
    });

    const { data: userData } = useGraphQLQuery<UserListQuery>(
        [QUERY_KEY.USERS],
        USER_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [`status_id:=(${ItemStatus.ACTIVE})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const createMutation = useGraphQLMutation<{}, { body: RoyaltyParamOption }>(ROYALTY_PARAMS_OPTIONS_CREATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.ROYALTY_PARAMS],
            });
        },
        onError: () => showToast(false, [t('error.common')]),
    });
    const updateMutation = useGraphQLMutation<{}, { id: number; body: RoyaltyParamOption }>(
        ROYALTY_PARAMS_OPTIONS_UPDATE,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                queryClient.invalidateQueries({
                    queryKey: [QUERY_KEY.ROYALTY_PARAMS],
                });
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const deleteMutation = useGraphQLMutation(ROYALTY_PARAMS_OPTIONS_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.ROYALTY_PARAMS],
            });

            onCloseWarningDelete(false);
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const userOptions = useMemo(
        () =>
            userData?.users_list.data.map((user) => ({
                label: user.full_name,
                value: user.id,
            })) || [],
        [userData]
    );

    const addParams = () => {
        append({
            display_order: '',
            name: '',
            value: '',
            user_ids: [],
        });
    };

    const onSave = async (index: number) => {
        const values = getValues(`options.${index}`);
        const isValid = await trigger(`options.${index}`);

        const fieldState = await getFieldState(`options.${index}`);

        if (!isValid) {
            setFieldState({
                error: {
                    [index]: {
                        display_order:
                            fieldState.error && 'display_order' in fieldState.error
                                ? (fieldState.error.display_order as FieldError)
                                : undefined,
                        name:
                            fieldState.error && 'name' in fieldState.error
                                ? (fieldState.error.name as FieldError)
                                : undefined,
                        value:
                            fieldState.error && 'value' in fieldState.error
                                ? (fieldState.error.value as FieldError)
                                : undefined,
                        ratio:
                            fieldState.error && 'ratio' in fieldState.error
                                ? (fieldState.error.ratio as FieldError)
                                : undefined,
                    },
                },
            });
            return;
        }

        setFieldState({
            error: {},
        });

        const payload = {
            ...values,
            display_order: Number(values.display_order),
            ratio: Number(values.ratio),
            royalty_param_id: royaltyParamId || 0,
        };

        delete payload.id;

        if (values.id) {
            updateMutation.mutate({
                id: values.id,
                body: payload,
            });
            return;
        }

        createMutation.mutate({
            body: payload,
        });
    };

    const onOpenWarningDelete = (index: number) => {
        const values = getValues(`options.${index}`);
        const id = values.id || 0;

        if (!id) {
            remove(index);
            return;
        }

        setIsOpenWarningDelete(true);
        setSelectedRowId(id);
    };

    const onCloseWarningDelete = (isOpen: boolean) => {
        setIsOpenWarningDelete(isOpen);
        setSelectedRowId(0);
    };

    const onDelete = () => {
        if (selectedRowId) {
            deleteMutation.mutate({
                id: selectedRowId,
            });
        }
    };

    useEffect(() => {
        if (isEmpty(options)) return;

        reset({
            options: options,
        });
    }, [options, reset]);

    return (
        <>
            <div className="table-responsive">
                <table className="table mb-0">
                    <thead>
                        <tr>
                            <th
                                className="text-center !px-[12px] w-[96px]"
                                style={{
                                    verticalAlign: 'middle',
                                }}
                            >
                                Thứ tự
                            </th>
                            <th
                                style={{
                                    verticalAlign: 'middle',
                                }}
                            >
                                Tên
                            </th>
                            <th
                                className="!px-[12px] w-[12%]"
                                style={{
                                    verticalAlign: 'middle',
                                }}
                            >
                                Giá trị
                            </th>
                            <th
                                className="!px-[12px] w-[12%]"
                                style={{
                                    verticalAlign: 'middle',
                                }}
                            >
                                Trọng số
                            </th>
                            <th
                                className="!px-[12px] w-[30%]"
                                style={{
                                    verticalAlign: 'middle',
                                }}
                            >
                                Nhân viên
                            </th>
                            <th
                                className="!px-[12px] text-center w-[96px]"
                                style={{
                                    verticalAlign: 'middle',
                                }}
                            >
                                <button
                                    type="button"
                                    title="Thêm"
                                    className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                    onClick={addParams}
                                >
                                    <Plus size={14} />
                                </button>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {isEmpty(fields) ? (
                            <tr>
                                <td colSpan={6} className="text-center !px-[12px]">
                                    Không có dữ liệu để hiển thị
                                </td>
                            </tr>
                        ) : (
                            <>
                                {fields.map((item, index) => (
                                    <tr key={item.id}>
                                        <td
                                            className="text-center !px-[12px]"
                                            style={{
                                                verticalAlign: 'baseline',
                                            }}
                                        >
                                            <Controller
                                                name={`options.${index}.display_order`}
                                                control={control}
                                                render={({ field }) => (
                                                    <>
                                                        <input
                                                            {...field}
                                                            className="form-control"
                                                            type="number"
                                                            onChange={(e) => field.onChange(Number(e.target.value))}
                                                        />
                                                        <span className="error">
                                                            {t(
                                                                fieldState?.error?.[index]?.display_order?.message || ''
                                                            )}
                                                        </span>
                                                    </>
                                                )}
                                            />
                                        </td>
                                        <td
                                            className="!px-[12px]"
                                            style={{
                                                verticalAlign: 'baseline',
                                            }}
                                        >
                                            <Controller
                                                name={`options.${index}.name`}
                                                control={control}
                                                render={({ field }) => (
                                                    <>
                                                        <input {...field} className="form-control" type="text" />
                                                        <span className="error">
                                                            {t(fieldState?.error?.[index]?.name?.message || '')}
                                                        </span>
                                                    </>
                                                )}
                                            />
                                        </td>
                                        <td
                                            className="!px-[12px]"
                                            style={{
                                                verticalAlign: 'baseline',
                                            }}
                                        >
                                            <Controller
                                                name={`options.${index}.value`}
                                                control={control}
                                                render={({ field }) => (
                                                    <>
                                                        <input {...field} className="form-control" type="number" />
                                                        <span className="error">
                                                            {t(fieldState?.error?.[index]?.value?.message || '')}
                                                        </span>
                                                    </>
                                                )}
                                            />
                                        </td>
                                        <td
                                            className="!px-[12px]"
                                            style={{
                                                verticalAlign: 'baseline',
                                            }}
                                        >
                                            <Controller
                                                name={`options.${index}.ratio`}
                                                control={control}
                                                render={({ field }) => (
                                                    <>
                                                        <input
                                                            {...field}
                                                            className="form-control"
                                                            type="number"
                                                            onChange={(e) => field.onChange(Number(e.target.value))}
                                                        />
                                                        <span className="error">
                                                            {t(fieldState?.error?.[index]?.ratio?.message || '')}
                                                        </span>
                                                    </>
                                                )}
                                            />
                                        </td>
                                        <td
                                            className="!px-[12px]"
                                            style={{
                                                verticalAlign: 'baseline',
                                            }}
                                        >
                                            <Controller
                                                name={`options.${index}.user_ids`}
                                                control={control}
                                                render={({ field }) => (
                                                    <Select
                                                        value={userOptions.filter((option) =>
                                                            field.value?.includes(option.value || 0)
                                                        )}
                                                        options={userOptions}
                                                        menuPortalTarget={document.body}
                                                        isMulti
                                                        onChange={(selectedOptions) => {
                                                            field.onChange(
                                                                selectedOptions
                                                                    ? selectedOptions.map((option) => option.value)
                                                                    : []
                                                            );
                                                        }}
                                                        closeMenuOnSelect={false}
                                                    />
                                                )}
                                            />
                                        </td>
                                        <td
                                            className="text-center !px-[12px] w-[5%]"
                                            style={{
                                                verticalAlign: 'baseline',
                                            }}
                                        >
                                            <div className="d-flex justify-content-center gap-[4px]">
                                                <button
                                                    type="button"
                                                    title="Xoá"
                                                    className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                                    onClick={() => onSave(index)}
                                                >
                                                    <Save size={14} />
                                                </button>
                                                <button
                                                    type="button"
                                                    title="Xoá"
                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                    onClick={() => onOpenWarningDelete(index)}
                                                >
                                                    <Trash2 size={14} />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </>
                        )}
                    </tbody>
                </table>
                <span className="error">{t(errors?.options?.message || '')}</span>
            </div>
            <ModalConfirm
                show={isOpenWarningDelete}
                text={t('confirm.delete')}
                btnDisabled={deleteMutation.isPending}
                changeShow={onCloseWarningDelete}
                submitAction={onDelete}
            />
        </>
    );
};

export default RoyaltyParamOptionsTable;
