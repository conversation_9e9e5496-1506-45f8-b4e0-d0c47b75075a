import { yupResolver } from '@hookform/resolvers/yup';
import InputSwitch from 'components/partials/InputSwitch';
import UpdateButton from 'components/partials/UpdateButton';
import { isEmpty } from 'lodash';
import { useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Select from 'react-select';
import { ArticleTypeNames, ItemStatus } from 'types/common/Item';
import { RoyaltyParams, RoyaltyParamsForm } from 'types/RoyaltyParams';
import cn from 'utils/cn';
import * as yup from 'yup';

type Props = {
    onSubmit: (values: RoyaltyParamsForm) => void;
    selectedRoyaltyParam?: RoyaltyParams;
    isLoading?: boolean;
    isOpen: boolean;
};

const schema = yup
    .object({
        name: yup.string().required('error.required').trim(),
        display_order: yup.string().trim().required('error.required').typeError('error.number'),
        desc: yup.string().optional(),
        status_id: yup.mixed().oneOf(Object.values(ItemStatus)),
        article_type_ids: yup.array().of(yup.number()).optional(),
    })
    .required();

const defaultValues = {
    display_order: '',
    name: '',
    desc: '',
    status_id: ItemStatus.PENDING,
    article_type_ids: [],
    values: [],
};

const RoyaltyParamsContent = ({ onSubmit, selectedRoyaltyParam, isLoading, isOpen }: Props) => {
    const { t } = useTranslation();

    const { control, handleSubmit, reset } = useForm<RoyaltyParamsForm>({
        resolver: yupResolver(schema),
        defaultValues,
    });

    const articleTypeOptions = useMemo(
        () =>
            ArticleTypeNames.map((item) => ({
                label: t(`${item.name}.single`),
                value: item.id,
            })),
        [t]
    );

    useEffect(() => {
        if (isEmpty(selectedRoyaltyParam)) return;

        reset({
            display_order: selectedRoyaltyParam.display_order.toString(),
            name: selectedRoyaltyParam.name,
            desc: selectedRoyaltyParam.desc,
            status_id: selectedRoyaltyParam.status_id,
            article_type_ids: selectedRoyaltyParam.article_type_ids,
            values: selectedRoyaltyParam.values,
        });
    }, [selectedRoyaltyParam, reset]);

    useEffect(() => () => reset(defaultValues), [isOpen, reset]);

    return (
        <div className="card-body">
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="row">
                    <div className="col-12 mb-1">
                        <label className="form-label">
                            Thứ tự hiển thị <span className="error">*</span>
                        </label>
                        <Controller
                            name="display_order"
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                                <>
                                    <input
                                        {...field}
                                        className={cn('form-control', {
                                            'is-invalid': error?.message,
                                        })}
                                        type="number"
                                    />
                                    <span className="error">{t(error?.message || '')}</span>
                                </>
                            )}
                        />
                    </div>
                    <div className="col-12 mb-1">
                        <label className="form-label">
                            Tên tham số <span className="error">*</span>
                        </label>
                        <Controller
                            name="name"
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                                <>
                                    <input
                                        {...field}
                                        type="text"
                                        className={cn('form-control', {
                                            'is-invalid': error?.message,
                                        })}
                                    />
                                    <span className="error">{t(error?.message || '')}</span>
                                </>
                            )}
                        />
                    </div>
                    <div className="col-12 mb-1">
                        <label className="form-label">Mô tả</label>
                        <Controller
                            name="desc"
                            control={control}
                            render={({ field }) => (
                                <>
                                    <textarea {...field} className="form-control" />
                                </>
                            )}
                        />
                    </div>
                    <div className="col-12 mb-1">
                        <label className="form-label">Loại tin</label>
                        <Controller
                            name="article_type_ids"
                            control={control}
                            render={({ field }) => (
                                <Select
                                    onChange={(selectedOptions) => {
                                        field.onChange(
                                            selectedOptions ? selectedOptions.map((option) => option.value) : []
                                        );
                                    }}
                                    value={articleTypeOptions.filter((option) => field.value?.includes(option.value))}
                                    closeMenuOnSelect={false}
                                    options={articleTypeOptions}
                                    isMulti
                                />
                            )}
                        />
                    </div>
                    <div className="col-12 mb-1">
                        <Controller
                            control={control}
                            name="status_id"
                            render={({ field }) => (
                                <InputSwitch
                                    name="status_id"
                                    labelFieldName="Trạng thái"
                                    labelSwitchName="Hoạt động"
                                    checked={field.value === ItemStatus.ACTIVE}
                                    onChange={(e) =>
                                        field.onChange(e.target.checked ? ItemStatus.ACTIVE : ItemStatus.PENDING)
                                    }
                                />
                            )}
                        />
                    </div>

                    <div>
                        <UpdateButton btnText="Cập nhật" isLoading={isLoading} />
                    </div>
                </div>
            </form>
        </div>
    );
};

export default RoyaltyParamsContent;
