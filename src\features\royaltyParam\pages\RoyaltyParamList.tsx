import { keepPreviousData, useQueryClient } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import ModalContent from 'components/partials/ModalContent';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import useQueryParams from 'hooks/useQueryParams';
import { isEmpty, isUndefined, omitBy } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { Edit, Trash2 } from 'react-feather';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Nav, NavItem, NavLink } from 'reactstrap';
import {
    ROYALTY_PARAMS_CREATE,
    ROYALTY_PARAMS_DELETE,
    ROYALTY_PARAMS_LIST,
    ROYALTY_PARAMS_UPDATE,
} from 'services/RoyaltyParam';
import { useAppStore } from 'stores/appStore';
import { baseFilterConfig } from 'types/common';
import { ArticleTypeNames, ItemStatusNames } from 'types/common/Item';
import { SearchLayoutParam } from 'types/Layout';
import { RoyaltyParams, RoyaltyParamsForm, RoyaltyParamsQuery } from 'types/RoyaltyParams';
import { generateFilters, getFieldHtml, showToast } from 'utils/common';
import RoyaltyParamOptionsTable from '../components/RoyaltyParamOptionsTable';
import RoyaltyParamsContent from '../components/RoyaltyParamsContent';

export default function RoyaltyParamList() {
    const departmentId = useAppStore((state) => state.departmentId);
    const [isOpenUpdateModal, setIsOpenUpdateModal] = useState(false);
    const [isOpenWarningDelete, setIsOpenWarningDelete] = useState(false);
    const [selectedRow, setSelectedRow] = useState<RoyaltyParams | undefined>();
    const [activeTab, setActiveTab] = useState(0);

    const { t } = useTranslation();
    const queryClient = useQueryClient();

    const { queryParams, setQueryParams } = useQueryParams();
    const paramConfig: SearchLayoutParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            department_id: departmentId.toString(),
        },
        isUndefined
    );

    const { limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, baseFilterConfig);

    const { data } = useGraphQLQuery<RoyaltyParamsQuery>(
        [QUERY_KEY.ROYALTY_PARAMS, paramConfig, filters],
        ROYALTY_PARAMS_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            filters: filters.length > 0 ? filters : undefined,
            sorts: ['display_order:ASC'],
        },
        '',
        {
            enabled: !!paramConfig.department_id,
            placeholderData: keepPreviousData,
        }
    );

    const createMutation = useGraphQLMutation<{}, { body: RoyaltyParams }>(ROYALTY_PARAMS_CREATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.ROYALTY_PARAMS],
            });

            onCloseUpdateModal(false);
        },
        onError: () => showToast(false, [t('error.common')]),
    });
    const updateMutation = useGraphQLMutation<{}, { id: number; body: RoyaltyParams }>(ROYALTY_PARAMS_UPDATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.ROYALTY_PARAMS],
            });

            onCloseUpdateModal(false);
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const deleteMutation = useGraphQLMutation(ROYALTY_PARAMS_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.ROYALTY_PARAMS],
            });

            onCloseWarningDelete(false);
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const royaltyParams = useMemo(() => data?.royalty_params_list?.data || [], [data]);

    const currentRoyaltyParam = useMemo(
        () => royaltyParams.find((param) => param.id === activeTab),
        [activeTab, royaltyParams]
    );

    const handleEdit = (item?: RoyaltyParams) => {
        if (!item) return;
        setSelectedRow(item);
        onOpenUpdateModal();
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const onOpenUpdateModal = () => {
        setIsOpenUpdateModal(true);
    };

    const onCloseUpdateModal = (isOpen: boolean) => {
        setIsOpenUpdateModal(isOpen);
        setSelectedRow(undefined);
    };

    const onSubmit = (data: RoyaltyParamsForm) => {
        const body = {
            ...data,
            display_order: Number(data.display_order),
            department_id: departmentId,
        };

        if (!isEmpty(selectedRow)) {
            updateMutation.mutate({
                id: selectedRow.id || 0,
                body,
            });
        } else {
            createMutation.mutate({
                body,
            });
        }
    };

    const onOpenWarningDelete = (item?: RoyaltyParams) => {
        if (!item) return;
        setIsOpenWarningDelete(true);
        setSelectedRow(item);
    };

    const onCloseWarningDelete = (isOpen: boolean) => {
        setIsOpenWarningDelete(isOpen);
        setSelectedRow(undefined);
    };

    const onDelete = () => {
        if (selectedRow?.id) {
            deleteMutation.mutate({
                id: selectedRow.id,
            });
        }
    };

    useEffect(() => {
        if (isEmpty(royaltyParams)) return;

        setActiveTab(royaltyParams[0].id || 0);
    }, [royaltyParams]);

    const onTabChange = (id?: number) => {
        setActiveTab(id || 0);
    };

    return (
        <>
            <Helmet>
                <title>Tham số nhuận bút</title>
            </Helmet>
            <ContentHeader
                title="Tham số nhuận bút"
                {...(royaltyParams?.length <= 5 && {
                    contextMenu: [
                        {
                            text: 'Thêm tham số',
                            icon: 'PLUS',
                            fnCallBack: {
                                actionMenu: () => onOpenUpdateModal(),
                            },
                        },
                    ],
                })}
            />

            <div className="content-body">
                <div className="card px-2 py-1 mb-0 d-flex flex-column gap-2">
                    {isEmpty(royaltyParams) ? (
                        <div className="text-center">Không có tham số nhuận bút để hiển thị.</div>
                    ) : (
                        <>
                            <Nav tabs className="mb-0">
                                {royaltyParams.map((item) => (
                                    <NavItem key={item.id}>
                                        <NavLink
                                            className={activeTab === item.id ? 'active' : ''}
                                            onClick={() => onTabChange(item.id)}
                                        >
                                            {item.name}
                                        </NavLink>
                                    </NavItem>
                                ))}
                            </Nav>

                            <div>
                                <div className="d-flex justify-content-between align-items-center">
                                    <p>Thông tin chi tiết tham số nhuận bút</p>
                                    <div className="d-flex gap-[4px]">
                                        <button
                                            type="button"
                                            title="Xoá"
                                            className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                            onClick={() => handleEdit(currentRoyaltyParam)}
                                        >
                                            <Edit size={14} />
                                        </button>
                                        <button
                                            type="button"
                                            title="Xoá"
                                            className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                            onClick={() => onOpenWarningDelete(currentRoyaltyParam)}
                                        >
                                            <Trash2 size={14} />
                                        </button>
                                    </div>
                                </div>
                                <div className="table-responsive">
                                    <table className="table">
                                        <thead>
                                            <tr>
                                                <th className="bg-white w-[200px]"></th>
                                                <th className="bg-white"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Thứ tự hiển thị:</td>
                                                <td>{currentRoyaltyParam?.display_order}</td>
                                            </tr>
                                            <tr>
                                                <td>Tên tham số:</td>
                                                <td>{currentRoyaltyParam?.name}</td>
                                            </tr>
                                            <tr>
                                                <td>Loại tin:</td>
                                                <td>
                                                    {ArticleTypeNames.filter((item) =>
                                                        currentRoyaltyParam?.article_type_ids?.includes(item.id)
                                                    )
                                                        .map((item) => t(`${item.name}.single`))
                                                        .join(', ')}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Trạng thái:</td>
                                                <td>
                                                    {getFieldHtml(
                                                        ItemStatusNames,
                                                        currentRoyaltyParam?.status_id as number,
                                                        t
                                                    )}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td
                                                    style={{
                                                        verticalAlign: 'baseline',
                                                    }}
                                                >
                                                    Mô tả:
                                                </td>
                                                <td>{currentRoyaltyParam?.desc}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <RoyaltyParamOptionsTable
                                royaltyParamId={activeTab}
                                options={currentRoyaltyParam?.options || []}
                            />
                        </>
                    )}
                </div>
            </div>

            <ModalContent
                show={isOpenUpdateModal}
                changeShow={onCloseUpdateModal}
                title={isEmpty(selectedRow) ? 'Thêm tham số' : 'Chỉnh sửa tham số'}
                content={
                    <RoyaltyParamsContent
                        selectedRoyaltyParam={selectedRow}
                        onSubmit={onSubmit}
                        isLoading={createMutation.isPending || updateMutation.isPending}
                        isOpen={isOpenUpdateModal}
                    />
                }
                modalSize="md"
            />

            <ModalConfirm
                show={isOpenWarningDelete}
                text={t('confirm.delete')}
                btnDisabled={deleteMutation.isPending}
                changeShow={onCloseWarningDelete}
                submitAction={onDelete}
            />
        </>
    );
}
