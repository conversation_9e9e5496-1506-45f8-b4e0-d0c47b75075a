import { gql } from 'graphql-request';

export const ROYALTY_PARAMS_LIST = gql`
    query Royalty_params_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!]) {
        royalty_params_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                desc
                status_id
                display_order
                article_type_ids
                options {
                    id
                    name
                    value
                    ratio
                    display_order
                    user_ids
                }
            }
        }
    }
`;

export const ROYALTY_PARAMS_CREATE = gql`
    mutation Royalty_params_create($body: RoyaltyParamSaveInputDto!) {
        royalty_params_create(body: $body) {
            id
            name
            desc
            values
            status_id
            display_order
        }
    }
`;

export const ROYALTY_PARAMS_UPDATE = gql`
    mutation Royalty_params_update($id: Int!, $body: RoyaltyParamSaveInputDto!) {
        royalty_params_update(id: $id, body: $body) {
            id
        }
    }
`;

export const ROYALTY_PARAMS_DELETE = gql`
    mutation Royalty_params_delete($id: Int!) {
        royalty_params_delete(id: $id)
    }
`;

export const ROYALTY_PARAMS_OPTIONS_CREATE = gql`
    mutation Royalty_param_options_create($body: RoyaltyParamOptionSaveInputDto!) {
        royalty_param_options_create(body: $body) {
            id
            name
            value
            ratio
            display_order
            user_ids
            royalty_param_id
        }
    }
`;

export const ROYALTY_PARAMS_OPTIONS_UPDATE = gql`
    mutation Royalty_param_options_update($id: Int!, $body: RoyaltyParamOptionSaveInputDto!) {
        royalty_param_options_update(id: $id, body: $body) {
            id
            name
            value
            ratio
            display_order
            user_ids
            royalty_param_id
        }
    }
`;

export const ROYALTY_PARAMS_OPTIONS_DELETE = gql`
    mutation Royalty_param_options_delete($id: Int!) {
        royalty_param_options_delete(id: $id)
    }
`;
