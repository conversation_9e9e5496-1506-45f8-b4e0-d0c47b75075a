import { BaseModel, DataList } from './common';
import { ItemStatus } from './common/Item';

export interface RoyaltyParams extends BaseModel {
    name: string;
    desc: string;
    options: RoyaltyParamOptionsForm[];
    status_id: ItemStatus;
    display_order: number;
    department_id: number;
    article_type_ids?: number[];
    values: never[];
}

export interface RoyaltyParamsQuery {
    royalty_params_list: DataList<RoyaltyParams>;
}

export interface RoyaltyParamsForm {
    name: string;
    desc: string;
    options: RoyaltyParamOptionsForm[];
    status_id: ItemStatus;
    display_order: string;
    article_type_ids?: number[];
    values: never[];
}
export interface RoyaltyParamOptionsForm {
    value?: string;
    display_order?: string;
    name?: string;
    user_ids?: number[];
    ratio?: number;
    id?: number;
}

export interface RoyaltyParamOption extends BaseModel {
    value?: string;
    display_order?: number;
    name?: string;
    user_ids?: number[];
    ratio?: number;
    royalty_param_id?: number;
}
